'use client'

import React, { useEffect, useRef, useState, memo } from 'react'

interface CryptoCoinsHeatmapProps {
  className?: string;
}

function CryptoCoinsHeatmap({ className = '' }: CryptoCoinsHeatmapProps) {
  const container = useRef<HTMLDivElement>(null)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!container.current) return
    
    const loadScript = () => {
      try {
        const script = document.createElement('script')
        script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-crypto-coins-heatmap.js'
        script.type = 'text/javascript'
        script.async = true
        
        script.onload = () => {
          setIsLoading(false)
          setError(null)
        }
        
        script.onerror = () => {
          setError('Error loading TradingView widget. Please try again.')
          setIsLoading(false)
        }
        
        script.innerHTML = JSON.stringify({
          dataSource: 'Crypto',
          blockSize: 'market_cap_calc',
          blockColor: '24h_close_change|5',
          locale: 'en',
          symbolUrl: '',
          colorTheme: 'light',
          hasTopBar: false,
          isDataSetEnabled: false,
          isZoomEnabled: true,
          hasSymbolTooltip: true,
          isMonoSize: false,
          width: '100%',
          height: '100%'
        })

        if (container.current) {
          container.current.innerHTML = ''
          container.current.appendChild(script)
        }
      } catch (err) {
        setError('Error initializing the widget.')
        setIsLoading(false)
      }
    }

    // Delay to avoid loading issues
    const timer = setTimeout(loadScript, 300)

    return () => {
      clearTimeout(timer)
      if (container.current) {
        container.current.innerHTML = ''
      }
    }
  }, [])

  if (error) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-md ${className}`}>
        <p className="text-red-600">{error}</p>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className={`w-full ${className}`} style={{ height: '600px' }}>
      {isLoading && (
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500" />
        </div>
      )}
      <div
        className="tradingview-widget-container"
        ref={container}
        style={{ 
          height: '100%', 
          width: '100%',
          display: isLoading ? 'none' : 'block'
        }}
      >
        <div
          className="tradingview-widget-container__widget"
          style={{ 
            height: 'calc(100% - 32px)', 
            width: '100%',
            minHeight: '500px' 
          }}
        />
        <div className="tradingview-widget-copyright">
          <a
            href="https://www.tradingview.com/"
            rel="noopener nofollow"
            target="_blank"
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Track all markets on TradingView
          </a>
        </div>
      </div>
    </div>
  )
}

export default memo(CryptoCoinsHeatmap)
