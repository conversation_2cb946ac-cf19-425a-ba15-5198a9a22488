'use client';

import type { UIMessage } from 'ai';
import cx from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import { memo, useState } from 'react';
import type { Vote } from '@/lib/db/schema';
import { DocumentToolCall, DocumentToolResult } from './document';
import { PencilEditIcon, SparklesIcon } from './icons';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { Weather } from './weather';
import equal from 'fast-deep-equal';
import { cn, sanitizeText } from '@/lib/utils';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { MessageEditor } from './message-editor';
import { DocumentPreview } from './document-preview';
import { MessageReasoning } from './message-reasoning';
import type { UseChatHelpers } from '@ai-sdk/react';
import { MemoryManager } from './memory-manager';
import { SearchLoadingState } from './ui/search-loading-state';
import { YouTubeLoadingState } from './ui/youtube-loading-state';
import { MapLoadingState } from './ui/map-loading-state';
import { Memory, MagnifyingGlass } from '@phosphor-icons/react';
import { SourceCitation } from './source-citation';
import { ImageGallery } from './image-gallery';
import { MessageAnnotations } from './chat/message-annotations';
import VideosComponent from './videos/VideosComponent';
import FinancialChart from './FinancialChart';
import StockNews from './StockNews';
import StockScreener from './StockScreener';
import HeatmapsMarket from './HeatmapsMarket';
import CryptoCoinsHeatmap from './CryptoCoinsHeatmap';
import ETFHeatmap from './ETFHeatmap';
import ForexCrossRates from './ForexCrossRates';
import ForexHeatmap from './ForexHeatmap';
import CryptocurrencyMarket from './CryptocurrencyMarket';
import StockFinancials from './StockFinancials';
import StockPrice from './StockPrice';
import MarketTrending from './MarketTrending';

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
}: {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');

  // Fonction supprimée car non utilisée

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            {
              'w-full': mode === 'edit',
              'group-data-[role=user]/message:w-fit': mode !== 'edit',
            },
          )}
        >
          {message.role === 'assistant' && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background">
              <div className="translate-y-px">
                <SparklesIcon size={14} />
              </div>
            </div>
          )}

          <div className="flex flex-col gap-4 w-full">
            {message.experimental_attachments && (
              <div
                data-testid={`message-attachments`}
                className="flex flex-row justify-end gap-2"
              >
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={attachment}
                  />
                ))}
              </div>
            )}

            {/* Display message annotations (like YouTube search results) */}
            {message.annotations && message.annotations.length > 0 && (
              <MessageAnnotations annotations={message.annotations} />
            )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === 'reasoning') {
                return (
                  <MessageReasoning
                    key={key}
                    isLoading={isLoading}
                    reasoning={part.reasoning}
                  />
                );
              }

              if (type === 'text') {
                if (mode === 'view') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === 'user' && !isReadonly && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              data-testid="message-edit-button"
                              variant="ghost"
                              className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                              onClick={() => {
                                setMode('edit');
                              }}
                            >
                              <PencilEditIcon />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit message</TooltipContent>
                        </Tooltip>
                      )}

                      <div
                        data-testid="message-content"
                        className={cn('flex flex-col gap-4', {
                          'bg-primary text-primary-foreground px-3 py-2 rounded-xl':
                            message.role === 'user',
                        })}
                      >
                        <Markdown>{sanitizeText(part.text)}</Markdown>
                      </div>
                    </div>
                  );
                }

                if (mode === 'edit') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        reload={reload}
                      />
                    </div>
                  );
                }
              }

              if (type === 'tool-invocation') {
                const { toolInvocation } = part;
                const { toolName, toolCallId, state } = toolInvocation;

                // Only show loading state if this is the most recent call of its type
                const isMostRecentCall =
                  message.parts
                    ?.filter(
                      (p) =>
                        p.type === 'tool-invocation' &&
                        p.toolInvocation?.toolName === toolName,
                    )
                    ?.sort((a, b) =>
                      (b.toolInvocation?.toolCallId || '').localeCompare(
                        a.toolInvocation?.toolCallId || '',
                      ),
                    )[0]?.toolInvocation?.toolCallId === toolCallId;

                if (state === 'call' && isMostRecentCall) {
                  const { args } = toolInvocation;

                  return (
                    <div
                      key={toolCallId}
                      className={cx({
                        skeleton: ['getWeather'].includes(toolName),
                      })}
                    >
                      {toolName === 'getWeather' ? (
                        <Weather />
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview isReadonly={isReadonly} args={args} />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolCall
                          type="update"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolCall
                          type="request-suggestions"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'retrieve' ? (
                        <SearchLoadingState
                          icon={Memory}
                          text="Searching the web..."
                          color="blue"
                        />
                      ) : toolName === 'web_search' ? (
                        <SearchLoadingState
                          icon={MagnifyingGlass}
                          text="Recherche sur le web..."
                          color="blue"
                        />
                      ) : toolName === 'youtube_search' ? (
                        <YouTubeLoadingState text="Recherche de vidéos YouTube..." />
                      ) : toolName === 'map_search' ? (
                        <MapLoadingState text="Recherche de lieux sur la carte..." />
                      ) : toolName === 'getStockChart' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement du graphique boursier pour{' '}
                            {toolInvocation.args?.ticker}...
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : toolName === 'getStockNews' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement des actualités pour{' '}
                            {toolInvocation.args?.ticker}...
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : toolName ===
                        'getStockScreener' ? // Don't show loading state for screener
                      // to avoid duplication with result rendering
                      null : toolName ===
                        'getHeatmapsMarket' ? // Don't show loading state for heatmap
                      // to avoid duplication with result rendering
                      null : toolName ===
                        'getCryptoCoinsHeatmap' ? // Don't show loading state for crypto heatmap
                      // to avoid duplication with result rendering
                      null : toolName ===
                        'getETFHeatmap' ? // Don't show loading state for ETF heatmap
                      // to avoid duplication with result rendering
                      null : toolName ===
                        'getForexCrossRates' ? // Don't show loading state for forex cross rates
                      // to avoid duplication with result rendering
                      null : toolName ===
                        'getForexHeatmap' ? // Don't show loading state for forex heatmap
                      // to avoid duplication with result rendering
                      null : toolName ===
                        'getCryptocurrencyMarket' ? // Don't show loading state for crypto market
                      // to avoid duplication with result rendering
                      null : toolName === 'getStockFinancials' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement des informations financières pour{' '}
                            {toolInvocation.args?.ticker}
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : toolName === 'getStockPrice' ? (
                        <div className="mt-4">
                          <div className="text-sm text-muted-foreground mb-2">
                            Chargement du cours de l'action pour{' '}
                            {toolInvocation.args?.ticker}
                          </div>
                          <div className="animate-pulse h-64 bg-muted rounded-lg" />
                        </div>
                      ) : null}
                    </div>
                  );
                }

                if (state === 'result') {
                  const { result } = toolInvocation;

                  return (
                    <div key={toolCallId}>
                      {toolName === 'getWeather' ? (
                        <Weather weatherAtLocation={result} />
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview
                          isReadonly={isReadonly}
                          result={result}
                        />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolResult
                          type="update"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolResult
                          type="request-suggestions"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'getStockChart' ? (
                        <div className="mt-4">
                          <FinancialChart
                            ticker={
                              result?.ticker || toolInvocation.args?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'getStockNews' ? (
                        <div className="mt-4">
                          <StockNews
                            ticker={
                              result?.ticker || toolInvocation.args?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'getStockScreener' &&
                        result?.type === 'stock_screener' ? (
                        <div
                          className="mt-4"
                          key={`stock-screener-${toolCallId}`}
                        >
                          <StockScreener />
                        </div>
                      ) : toolName === 'getHeatmapsMarket' &&
                        result?.type === 'heatmaps_market' ? (
                        <div
                          className="mt-4"
                          key={`heatmaps-market-${toolCallId}`}
                        >
                          <HeatmapsMarket />
                        </div>
                      ) : toolName === 'getCryptoCoinsHeatmap' &&
                        result?.type === 'crypto_coins_heatmap' ? (
                        <div
                          className="mt-4"
                          key={`crypto-coins-heatmap-${toolCallId}`}
                        >
                          <CryptoCoinsHeatmap />
                        </div>
                      ) : toolName === 'getETFHeatmap' &&
                        result?.type === 'etf_heatmap' ? (
                        <div className="mt-4" key={`etf-heatmap-${toolCallId}`}>
                          <ETFHeatmap />
                        </div>
                      ) : toolName === 'getForexCrossRates' &&
                        result?.type === 'forex_cross_rates' ? (
                        <div
                          className="mt-4"
                          key={`forex-cross-rates-${toolCallId}`}
                        >
                          <ForexCrossRates />
                        </div>
                      ) : toolName === 'getForexHeatmap' &&
                        result?.type === 'forex_heatmap' ? (
                        <div
                          className="mt-4"
                          key={`forex-heatmap-${toolCallId}`}
                        >
                          <ForexHeatmap />
                        </div>
                      ) : toolName === 'getCryptocurrencyMarket' &&
                        result?.type === 'cryptocurrency_market' ? (
                        <div
                          className="mt-4"
                          key={`cryptocurrency-market-${toolCallId}`}
                        >
                          <CryptocurrencyMarket />
                        </div>
                      ) : toolName === 'getStockFinancials' ? (
                        <div className="mt-4">
                          <StockFinancials
                            symbol={
                              result?.ticker || toolInvocation.args?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'getStockPrice' ? (
                        <div className="mt-4">
                          <StockPrice
                            symbol={
                              result?.ticker || toolInvocation.args?.ticker
                            }
                          />
                        </div>
                      ) : toolName === 'showMarketTrending' ? (
                        <div className="mt-4">
                          <MarketTrending />
                        </div>
                      ) : toolName === 'memory_manager' ? (
                        !result ? (
                          <SearchLoadingState
                            icon={Memory}
                            text="Managing memories..."
                            color="purple"
                          />
                        ) : (
                          <MemoryManager result={result} />
                        )
                      ) : toolName === 'retrieve' ? (
                        !result ? (
                          <SearchLoadingState
                            icon={Memory}
                            text="Searching the web..."
                            color="blue"
                          />
                        ) : (
                          <MemoryManager result={result} />
                        )
                      ) : toolName === 'web_search' ? (
                        result &&
                        typeof result === 'object' &&
                        'searches' in result ? (
                          <>
                            {/* Afficher les résultats de recherche avec images dans un seul cadre */}
                            <div className="mb-4">
                              {result.searches.some(
                                (search: { images?: any[] }) =>
                                  search.images && search.images.length > 0,
                              ) && (
                                <div className="mb-4">
                                  <h3 className="text-md font-medium mb-3 px-4">
                                    Images illustratives
                                  </h3>
                                  {/* Regrouper toutes les images dans un seul cadre */}
                                  <div>
                                    {(() => {
                                      // Collecter toutes les images de toutes les recherches
                                      const allImages = result.searches.flatMap(
                                        (search: {
                                          images?: any[];
                                          query: string;
                                        }) =>
                                          search.images &&
                                          Array.isArray(search.images)
                                            ? search.images.map((img: any) => ({
                                                ...(typeof img === 'string'
                                                  ? { url: img }
                                                  : img),
                                                searchQuery: search.query,
                                              }))
                                            : [],
                                      );

                                      // S'il y a des images, les afficher dans un seul ImageGallery
                                      return allImages.length > 0 ? (
                                        <ImageGallery
                                          images={allImages}
                                          query={
                                            result.searches[0]?.query || ''
                                          }
                                          maxPreviewImages={8}
                                        />
                                      ) : null;
                                    })()}
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Afficher les sources */}
                            <SourceCitation
                              sources={result.searches.flatMap(
                                (search: { results: any[] }) =>
                                  search.results.map(
                                    (item: {
                                      url: string;
                                      title: string;
                                      content: string;
                                    }) => ({
                                      url: item.url,
                                      title: item.title,
                                      content: item.content,
                                    }),
                                  ),
                              )}
                            />
                          </>
                        ) : null
                      ) : toolName === 'youtube_search' ? (
                        result &&
                        typeof result === 'object' &&
                        'videos' in result ? (
                          <div className="mt-2 mb-4">
                            {result.videos.length > 0 ? (
                              <div className="rounded-xl overflow-hidden shadow-sm">
                                <div className="flex items-center justify-between p-3 bg-white">
                                  <h3 className="text-md font-medium">
                                    Videos
                                  </h3>
                                  <button
                                    type="button"
                                    className="text-sm text-gray-500 hover:text-gray-700"
                                  >
                                    ×
                                  </button>
                                </div>
                                <VideosComponent videos={result.videos} />
                              </div>
                            ) : (
                              <p className="text-sm text-muted-foreground px-4">
                                Aucune vidéo YouTube trouvée pour cette
                                recherche.
                              </p>
                            )}
                          </div>
                        ) : null
                      ) : null}
                    </div>
                  );
                }
              }
            })}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;

    return true;
  },
);

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message "
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl',
          {
            'group-data-[role=user]/message:bg-muted': true,
          },
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <SparklesIcon size={14} />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            Hmm...
          </div>
        </div>
      </div>
    </motion.div>
  );
};
